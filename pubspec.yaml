name: bus_driver
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.0.1+52

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  shared_preferences: 2.3.5
  flutter_screenutil: ^5.9.3
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1
  firebase_core_web:
  flutter_rating_bar: ^4.0.1
  equatable: ^2.0.7
  fluttertoast: ^8.2.10
  easy_localization: ^3.0.7
  easy_localization_loader: ^2.0.2
  flutter_svg: ^2.0.16
  svg_path_parser: ^1.1.2
  touchable:
  pin_code_fields: ^8.0.1
  wakelock_plus: ^1.1.4
  mailer: ^6.1.0
  image_picker: ^1.1.2
  flutter_bloc: ^8.1.6
  dio: ^5.7.0
  flutter_osm_plugin: ^1.3.6
  geolocator: ^13.0.2
  socket_io_client: ^3.0.2
  json_serializable: ^6.9.2
  url_launcher: ^6.3.1
  firebase_core: 3.9.0
  firebase_messaging: 15.1.6
  flutter_local_notifications: 18.0.1
  carousel_slider: ^5.0.0
  get_it: 8.0.3
  flutter_background_service: ^5.0.10
  permission_handler: ^11.3.1
#  file_picker: 8.1.6
  logger: ^2.5.0
  google_maps_flutter: ^2.10.0
  # Removed workmanager due to compatibility issues
  # workmanager: ^0.5.2
  open_file: ^3.5.10
  # Removed open_file_plus due to compatibility issues
  # open_file_plus: ^3.4.1+1
  location: ^7.0.1
  android_intent_plus: ^4.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icons.png"
  min_sdk_android: 21

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/translations/
    - assets/images/
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
