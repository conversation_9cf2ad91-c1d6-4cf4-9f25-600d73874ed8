PODS:
  - Alamofire (5.10.2)
  - connectivity_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (11.4.0):
    - FirebaseCore (= 11.4.0)
  - Firebase/Messaging (11.4.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.4.0)
  - firebase_core (3.9.0):
    - Firebase/CoreOnly (= 11.4.0)
    - Flutter
  - firebase_messaging (15.1.6):
    - Firebase/Messaging (= 11.4.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.4.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.7.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.4.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.4.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_background_service_ios (0.0.3):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_osm_plugin (0.0.1):
    - Alamofire
    - Flutter
    - OSMFlutterFramework
    - Polyline
    - Yams
  - fluttertoast (0.0.2):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - location (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_file_ios (0.0.1):
    - Flutter
  - OSMFlutterFramework (0.8.1)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - Polyline (5.1.0)
  - PromisesObjC (2.4.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - Yams (5.0.6)

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_background_service_ios (from `.symlinks/plugins/flutter_background_service_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_osm_plugin (from `.symlinks/plugins/flutter_osm_plugin/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - Alamofire
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Google-Maps-iOS-Utils
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - nanopb
    - OSMFlutterFramework
    - Polyline
    - PromisesObjC
    - Yams

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_background_service_ios:
    :path: ".symlinks/plugins/flutter_background_service_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_osm_plugin:
    :path: ".symlinks/plugins/flutter_osm_plugin/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  Alamofire: 7193b3b92c74a07f85569e1a6c4f4237291e7496
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  Firebase: cf1b19f21410b029b6786a54e9764a0cacad3c99
  firebase_core: b62a5080210edad3f2934314a8b2c6f5124e8e10
  firebase_messaging: 98619a0572d82cfb3668e78859ba9f1110e268c9
  FirebaseCore: e0510f1523bc0eb21653cac00792e1e2bd6f1771
  FirebaseCoreInternal: d6c17dafc8dc33614733a8b52df78fcb4394c881
  FirebaseInstallations: 6ef4a1c7eb2a61ee1f74727d7f6ce2e72acf1414
  FirebaseMessaging: f8a160d99c2c2e5babbbcc90c4a3e15db036aee2
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_background_service_ios: e30e0d3ee69e4cee66272d0c78eacd48c2e94aac
  flutter_local_notifications: df98d66e515e1ca797af436137b4459b160ad8c9
  flutter_osm_plugin: 60be722901333b6107275c9c51ffb79167b6d99d
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  geolocator_apple: 66b711889fd333205763b83c9dcf0a57a28c7afd
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: e31555a04d1986ab130f2b9f24b6cdc861acc6d3
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  OSMFlutterFramework: ed251dbb5af79b383b807ee8e5ab2889d7e484c4
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  Polyline: 2a1f29f87f8d9b7de868940f4f76deb8c678a5b1
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  wakelock_plus: 76957ab028e12bfa4e66813c99e46637f367fc7e
  Yams: e10dae147f517ed57ecae37c5e8681bdf8fcab65

PODFILE CHECKSUM: 2c50c4b8a2f5c44bd8feeadb79d853821c100ea9

COCOAPODS: 1.16.2
